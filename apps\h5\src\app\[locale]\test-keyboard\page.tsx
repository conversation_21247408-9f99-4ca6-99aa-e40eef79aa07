'use client'

import { useRef, useState } from 'react'
import { Button } from 'antd-mobile'

import { QuantitySelector } from '@/businessComponents'
import { CustomPopup } from '@/components'

export default function TestKeyboardPage() {
  const [visible, setVisible] = useState(false)
  const popupRef = useRef<HTMLDivElement>(null)

  return (
    <div className="p-4">
      <Button color="primary" onClick={() => setVisible(true)} className="mb-4">
        测试
      </Button>

      <div ref={popupRef}>
        <CustomPopup
          showHeader
          visible={visible}
          onClose={() => setVisible(false)}
          headTitle="键盘适配测试"
          bodyStyle={{
            backgroundColor: '#F3F3F4',
            maxHeight: '65rem',
          }}
          footer={
            <Button color="primary" className="w-full" onClick={() => setVisible(false)}>
              确认
            </Button>
          }>
          <div className="space-y-6 p-4">
            <div>
              <label className="mb-2 block text-sm font-medium">项目名称</label>
              <input
                type="text"
                className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入项目名称"
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">描述</label>
              <textarea
                rows={4}
                className="w-full resize-none rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入描述信息"
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">邮箱</label>
              <input
                type="email"
                className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入邮箱地址"
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">电话</label>
              <input
                type="tel"
                className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入电话号码"
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">数量选择</label>
              <QuantitySelector
                value={1}
                inputMax={99}
                onQtyChange={(value, max) => console.log('数量变化:', value, max)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="font-miSansDemiBold450 text-base">数量</div>
              <QuantitySelector
                value={1}
                limitCount={null}
                inputMax={99}
                onQtyChange={(value, max) => console.log('数量变化:', value, max)}
              />
            </div>
          </div>
        </CustomPopup>
      </div>
    </div>
  )
}
